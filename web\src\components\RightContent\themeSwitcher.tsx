import { ConfigProvider } from 'antd';
import { isString, isPlainObject } from 'lodash'

const id = 'theme-style';
const attr = 'data-theme';
const storageKey = 'md-theme';
const prefetchId = 'theme-prefetch';

// 导入 defaultSettings
import Settings from '../../../config/defaultSettings';

// 更新 defaultSettings 的函数
const updateDefaultSettings = (theme: ThemeType): void => {
  // 根据主题更新 defaultSettings 的配置
  if (theme === 'dark') {
    // 暗色主题配置
    Settings.token = {
      ...Settings.token,
      "sider": {
        ...Settings.token?.sider,
        colorMenuBackground:'#0c0e11',
        "colorTextMenuTitle": "#E6E6E9",
        "colorTextMenu": "#C7C8C7",
        // "colorTextMenuSelected": "#C7C7CB",
        // "colorTextMenuActive": "#C7C7CB",
        "colorTextMenuSelected": "#87A9FF",
        "colorTextMenuActive": "#87A9FF",
        "colorTextMenuItemHover": "#C7C7CB",
        "colorBgMenuItemHover": "#2F3133",
        // "colorBgMenuItemSelected": "#87A9FF",
        "colorBgMenuItemSelected": "#2C2C2C",
        "colorBgCollapsedButton": "#2F3133",
        "colorMenuItemDivider":"#393B3C",
        colorTextMenuSecondary:'#C7C8C7',
        colorBgMenuItemCollapsedElevated:'#1a1c1e',
        colorTextCollapsedButton:'#C7C8C7',
        colorTextCollapsedButtonHover:'C7C8C7',
        
      },
      "pageContainer": {
        colorBgPageContainer: "#1a1c1e"//背景底色
      },
    };
    Settings.colorPrimary = "#2f3133"; // 选中项
    
    
  } else {
    // 亮色主题配置 - 匹配图片中的现代化设计
    Settings.token = {
      ...Settings.token,
      header: {
        ...Settings.token?.header,
        heightLayoutHeader: 56,
        colorBgHeader: "#ffffff",
        colorHeaderTitle: "#1f2937",
        colorTextRightActionsItem: "#6b7280"
      },
      sider: {
        ...Settings.token?.sider,
        colorMenuBackground: '#f8fafc',
        colorTextMenuTitle: "#1f2937",
        colorTextMenu: "#6b7280",
        colorTextMenuSelected: "#1f2937",
        colorTextMenuActive: "#1f2937",
        colorTextMenuItemHover: "#1f2937",
        colorBgMenuItemHover: "#f1f5f9",
        colorBgMenuItemSelected: "#e2e8f0",
        colorBgCollapsedButton: "#ffffff",
        colorMenuItemDivider: "#e2e8f0",
        colorTextMenuSecondary: '#9ca3af',
        colorBgMenuItemCollapsedElevated: '#ffffff',
        colorTextCollapsedButton: '#6b7280',
        colorTextCollapsedButtonHover: '#1f2937',
        paddingInlineLayoutMenu: 16,
        paddingBlockLayoutMenu: 8
      },
      pageContainer: {
        colorBgPageContainer: "#f8fafc",
        paddingInlinePageContainerContent: 24,
        paddingBlockPageContainerContent: 24
      }
    };
    Settings.colorPrimary = "#3b82f6"; // 现代蓝色主题
  }
  
  // 强制更新 ConfigProvider 配置
  ConfigProvider.config({
    theme: {
      token: Settings.token,
    }
  });
  
  // 触发自定义事件，通知其他组件主题已更改
  const event = new CustomEvent('themeChanged', { detail: { theme } });
  window.dispatchEvent(event);
};

type ThemeType = 'light' | 'dark';
type ThemeMap = Record<ThemeType, string>;
type CustomTheme = Record<string, any>;

const getThemeMap = (): ThemeMap => {
  const timestamp = new Date().getTime();
  return {
    dark: `/css/dark.css?${timestamp}`,
    light: `/css/light.css?${timestamp}`,
  };
};

const storeTheme = (theme: ThemeType): void => {
  localStorage.removeItem(storageKey);
  localStorage.setItem(storageKey, theme);
};

const removePreTheme = (): void => {
  const dom = document.getElementById(id);
  if (dom) {
    dom.remove();
  }
};

const getTheme = function getTheme(): ThemeType {
  return localStorage.getItem(storageKey) as ThemeType || 'light';
};

const switcher = ({ theme, useStorage = true }: { theme: ThemeType; useStorage?: boolean }): void => {
  removePreTheme();

  if (useStorage) {
    storeTheme(theme)
  };

  // 更新 defaultSettings 配置
  updateDefaultSettings(theme);

  const themeMap = getThemeMap();
  if (themeMap[theme]) {
    const style = document.createElement('link');
    style.type = 'text/css';
    style.rel = 'stylesheet';
    style.id = id;
    style.href = themeMap[theme];
    document.body.append(style);
  };

  document.body.setAttribute(attr, theme);
};

const prefetchTheme = (): void => {
  const themeMap = getThemeMap();
  const themes = Object.keys(themeMap) as ThemeType[];
  themes.forEach(function (theme) {
    const themeAssetId = ''.concat(prefetchId, '-').concat(theme);

    if (!document.getElementById(themeAssetId)) {
      // add prefetch
      const stylePrefetch = document.createElement('link');
      stylePrefetch.rel = 'prefetch';
      stylePrefetch.type = 'text/css';
      stylePrefetch.id = themeAssetId;
      stylePrefetch.href = themeMap[theme];
      document.head.append(stylePrefetch);
    }
  });
};

const initTheme = (): void => {
  let theme = getTheme();

  if (theme === 'dark' || theme === 'light') {
    switcher({
      theme,
      useStorage: true,
    });
  } else if (isString(theme)) {
    try {
      const parsedTheme = JSON.parse(theme) as CustomTheme;
      isPlainObject(parsedTheme) && ConfigProvider.config({
        theme: parsedTheme
      });
      setCustomColor(parsedTheme);
    } catch (error) {
      console.error('Failed to parse theme:', error);
    }
  }
};

// 驼峰转换中划线
function toLine(name: string): string {
  return name.replace(/([A-Z])/g, '-$1').toLowerCase();
}

const setCustomColor = (colorMap: CustomTheme): void => {
  if (!isPlainObject(colorMap)) return;
  const customList = [ 'headerColor', 'siderColor' ];
  Object.keys(colorMap).filter(key => customList.includes(key)).forEach(key => {
    const color = colorMap[key];
    document.body.style.setProperty(`--md-${toLine(key)}`, color);
  });
};

export {
  switcher,
  getTheme,
  removePreTheme,
  initTheme,
  storeTheme,
  prefetchTheme,
  getThemeMap,
  setCustomColor,
  updateDefaultSettings,
}