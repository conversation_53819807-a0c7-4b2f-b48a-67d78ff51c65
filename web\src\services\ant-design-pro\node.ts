import { request } from '@umijs/max';

// 检查部署节点IP
export async function checkDeployNodeIP(body: { ip: string }, options?: { [key: string]: any }) {
  return request<{
    success: boolean;
    data: any;
    message?: string;
  }>('/api/node/checkIP', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 注册部署节点
export async function registerDeployNode(body: { 
  ip: string; 
  name?: string; 
  description?: string; 
}, options?: { [key: string]: any }) {
  return request<{
    success: boolean;
    data: any;
    message?: string;
  }>('/api/node/register', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 获取部署节点列表
export async function getDeployNodeList(options?: { [key: string]: any }) {
  return request<{
    success: boolean;
    data: any[];
    message?: string;
  }>('/api/node/list', {
    method: 'GET',
    ...(options || {}),
  });
}

// 获取单个部署节点信息
export async function getDeployNode(body: { nodeId: string }, options?: { [key: string]: any }) {
  return request<{
    success: boolean;
    data: any;
    message?: string;
  }>('/api/node/get', {
    method: 'GET',
    params: body,
    ...(options || {}),
  });
}

// 更新部署节点信息
export async function updateDeployNode(body: { 
  nodeId: string; 
  name?: string; 
  description?: string; 
  status?: string; 
}, options?: { [key: string]: any }) {
  return request<{
    success: boolean;
    data: any;
    message?: string;
  }>('/api/node/update', {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}

// 删除部署节点
export async function removeDeployNode(body: { nodeId: string }, options?: { [key: string]: any }) {
  return request<{
    success: boolean;
    message?: string;
  }>('/api/node/remove', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}

// 检查节点状态
export async function checkNodeStatus(body: { nodeId: string }, options?: { [key: string]: any }) {
  return request<{
    success: boolean;
    data: {
      status: 'online' | 'offline' | 'error';
      lastCheck: string;
      details?: any;
    };
    message?: string;
  }>('/api/node/status', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}
