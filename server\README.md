# Keycloak Backend Service

基于Express.js和Keycloak的后端认证服务。

## 功能特性

- 🔐 Keycloak集成认证
- 🛡️ JWT Token验证
- 👥 基于角色的访问控制(RBAC)
- 🚀 RESTful API设计
- 📝 完整的错误处理
- 🔧 环境配置管理

## 快速开始

### 1. 安装依赖

```bash
cd backend
npm install
```

### 2. 环境配置

复制 `.env` 文件并根据你的Keycloak配置修改：

```bash
cp .env.example .env
```

配置项说明：
- `KEYCLOAK_URL`: Keycloak服务器地址
- `KEYCLOAK_REALM`: Keycloak域名
- `KEYCLOAK_CLIENT_ID`: 客户端ID
- `KEYCLOAK_CLIENT_SECRET`: 客户端密钥

### 3. 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

## API文档

### 认证相关

#### GET /api/auth/config
获取Keycloak配置信息

#### GET /api/auth/login
获取Keycloak登录URL

#### POST /api/auth/logout
用户登出

#### POST /api/auth/verify
验证JWT Token

### 用户相关

#### GET /api/user/current
获取当前用户信息（需要认证）

#### POST /api/user/login
传统登录（重定向到Keycloak）

#### POST /api/user/logout
用户登出

#### GET /api/user/permissions
获取用户权限

### 受保护的路由

#### GET /api/protected/dashboard
仪表板（需要认证）

#### GET /api/protected/admin
管理员区域（需要admin角色）

#### GET /api/protected/user-area
用户区域（需要认证）

#### GET /api/protected/system-info
系统信息（需要认证）

#### GET /api/protected/data
数据查询（需要认证）

### 管理员专用路由

#### /api/devops/*
Git项目管理相关接口（需要admin角色）
- 项目创建、删除、编辑
- 分支管理
- 代码仓库操作
- 合并请求管理
- 问题跟踪

#### /api/deploy/*
部署管理相关接口（需要admin角色）
- 配置检查
- 服务检查和启动
- 部署任务管理

#### /api/node/*
部署节点管理相关接口（需要admin角色）
- 节点IP检查和注册
- 节点状态管理

## 中间件

### 认证中间件
- `verifyToken`: JWT Token验证
- `requireRole(role)`: 需要特定角色
- `requireAnyRole(roles)`: 需要任一角色
- `requireAdmin`: 需要管理员角色（专用于管理员路由）

### 错误处理
- 统一错误响应格式
- 开发环境详细错误信息
- 异步错误处理

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t keycloak-backend .

# 运行容器
docker run -p 8001:8001 --env-file .env keycloak-backend
```

### 环境变量

生产环境需要设置的环境变量：
- `NODE_ENV=production`
- `SESSION_SECRET`: 强密码
- `KEYCLOAK_CLIENT_SECRET`: Keycloak客户端密钥

## 开发指南

### 项目结构

```
backend/
├── src/
│   ├── config/          # 配置文件
│   ├── middleware/      # 中间件
│   ├── routes/          # 路由
│   └── app.js          # 主应用文件
├── .env                # 环境配置
├── package.json        # 依赖配置
└── README.md          # 说明文档
```

### 添加新的受保护路由

1. 在 `routes/` 目录下创建新的路由文件
2. 使用认证中间件保护路由
3. 在 `app.js` 中注册路由

### 角色权限管理

在Keycloak中配置角色，然后在代码中使用：
- `requireRole('admin')`: 需要admin角色
- `requireAnyRole(['user', 'admin'])`: 需要user或admin角色

## 故障排除

### 常见问题

1. **Keycloak连接失败**
   - 检查KEYCLOAK_URL是否正确
   - 确认Keycloak服务是否运行

2. **JWT验证失败**
   - 检查客户端配置
   - 确认Token格式正确

3. **CORS错误**
   - 检查FRONTEND_URL配置
   - 确认CORS设置正确

## 许可证

MIT License
