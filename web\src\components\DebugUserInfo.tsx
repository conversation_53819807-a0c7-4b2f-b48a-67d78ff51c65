import React from 'react';
import { Card, Descriptions, Tag, Button, Space, message } from 'antd';
import { useModel, useAccess } from '@umijs/max';
import { keycloakService } from '@/services/keycloak';

const DebugUserInfo: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const access = useAccess();

  const refreshUserInfo = async () => {
    try {
      const userInfo = keycloakService.getUserInfo();
      console.log('Keycloak用户信息:', userInfo);
      console.log('当前用户状态:', initialState?.currentUser);
      console.log('权限状态:', access);
      message.success('用户信息已刷新，请查看控制台');
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败');
    }
  };

  const testToken = () => {
    const token = keycloakService.getToken();
    console.log('当前Token:', token);
    
    if (token) {
      try {
        // 解析token内容（仅用于调试）
        const payload = JSON.parse(atob(token.split('.')[1]));
        console.log('Token内容:', payload);
        message.success('Token信息已输出到控制台');
      } catch (error) {
        console.error('Token解析失败:', error);
        message.error('Token解析失败');
      }
    } else {
      message.warning('未找到Token');
    }
  };

  const currentUser = initialState?.currentUser;
  const keycloakUserInfo = keycloakService.getUserInfo();

  return (
    <Card title="用户权限调试信息" style={{ margin: '16px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Space>
          <Button type="primary" onClick={refreshUserInfo}>
            刷新用户信息
          </Button>
          <Button onClick={testToken}>
            检查Token
          </Button>
        </Space>

        <Card size="small" title="当前用户状态 (initialState)" type="inner">
          <Descriptions column={1} size="small">
            <Descriptions.Item label="用户ID">
              {currentUser?.userid || '未设置'}
            </Descriptions.Item>
            <Descriptions.Item label="用户名">
              {currentUser?.username || '未设置'}
            </Descriptions.Item>
            <Descriptions.Item label="姓名">
              {currentUser?.name || '未设置'}
            </Descriptions.Item>
            <Descriptions.Item label="邮箱">
              {currentUser?.email || '未设置'}
            </Descriptions.Item>
            <Descriptions.Item label="访问级别">
              <Tag color={currentUser?.access === 'owner' ? 'red' : 'blue'}>
                {currentUser?.access || '未设置'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="角色列表">
              {currentUser?.roles ? (
                <Space>
                  {currentUser.roles.map((role: string) => (
                    <Tag key={role} color="green">{role}</Tag>
                  ))}
                </Space>
              ) : (
                '未设置'
              )}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Card size="small" title="Keycloak用户信息" type="inner">
          <Descriptions column={1} size="small">
            <Descriptions.Item label="认证状态">
              <Tag color={keycloakService.isAuthenticated() ? 'green' : 'red'}>
                {keycloakService.isAuthenticated() ? '已认证' : '未认证'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="用户ID">
              {keycloakUserInfo?.id || '未获取'}
            </Descriptions.Item>
            <Descriptions.Item label="用户名">
              {keycloakUserInfo?.username || '未获取'}
            </Descriptions.Item>
            <Descriptions.Item label="姓名">
              {keycloakUserInfo?.name || '未获取'}
            </Descriptions.Item>
            <Descriptions.Item label="邮箱">
              {keycloakUserInfo?.email || '未获取'}
            </Descriptions.Item>
            <Descriptions.Item label="角色列表">
              {keycloakUserInfo?.roles ? (
                <Space>
                  {keycloakUserInfo.roles.map((role: string) => (
                    <Tag key={role} color="blue">{role}</Tag>
                  ))}
                </Space>
              ) : (
                '未获取'
              )}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Card size="small" title="权限检查结果" type="inner">
          <Descriptions column={1} size="small">
            <Descriptions.Item label="是否管理员">
              <Tag color={access.isAdmin ? 'green' : 'red'}>
                {access.isAdmin ? '是' : '否'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="可管理项目">
              <Tag color={access.canManageProjects ? 'green' : 'red'}>
                {access.canManageProjects ? '是' : '否'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="可管理部署">
              <Tag color={access.canManageDeploy ? 'green' : 'red'}>
                {access.canManageDeploy ? '是' : '否'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="可管理节点">
              <Tag color={access.canManageNodes ? 'green' : 'red'}>
                {access.canManageNodes ? '是' : '否'}
              </Tag>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Card size="small" title="调试说明" type="inner">
          <ul>
            <li>检查"当前用户状态"中的角色列表是否包含 owner/admin/maintainer</li>
            <li>检查"权限检查结果"中的"是否管理员"是否为"是"</li>
            <li>如果角色信息不正确，请检查Keycloak中的用户角色配置</li>
            <li>点击"刷新用户信息"按钮查看控制台输出的详细信息</li>
            <li>点击"检查Token"按钮查看当前的认证Token内容</li>
          </ul>
        </Card>
      </Space>
    </Card>
  );
};

export default DebugUserInfo;
