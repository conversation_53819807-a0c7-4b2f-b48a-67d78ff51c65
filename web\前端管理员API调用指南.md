# 前端管理员API调用指南

## 概述

本指南说明如何在前端调用需要管理员权限的API接口（`/api/devops`、`/api/deploy`、`/api/node`）。

## 认证机制

### 自动Token添加

前端已配置请求拦截器，会自动为所有请求添加Keycloak认证token：

```typescript
// web/src/requestErrorConfig.ts
requestInterceptors: [
  (config: RequestOptions) => {
    // 自动添加 Keycloak 认证 token
    const token = keycloakService.getToken();
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }
    return config;
  },
],
```

### 权限检查

使用Antd的权限控制系统检查用户是否有管理员权限：

```typescript
import { useAccess } from '@umijs/max';

const MyComponent = () => {
  const access = useAccess();
  const isAdmin = access.isAdmin;
  
  if (!isAdmin) {
    return <div>权限不足</div>;
  }
  
  // 管理员功能代码
};
```

## API服务使用

### 1. DevOps API (`/api/devops`)

```typescript
import { 
  listProjects, 
  createProject, 
  deleteProject,
  listBranch,
  createBranch
} from '@/services/ant-design-pro/gitlab';

// 获取项目列表
const projects = await listProjects();

// 创建项目
const newProject = await createProject({
  name: '项目名称',
  description: '项目描述',
  visibility: 'private'
});

// 删除项目
await deleteProject({ id: projectId });

// 获取分支列表
const branches = await listBranch({ id: projectId });

// 创建分支
await createBranch({
  id: projectId,
  branch: 'feature/new-feature',
  ref: 'main'
});
```

### 2. Deploy API (`/api/deploy`)

```typescript
import { 
  sendConfigCheck,
  sendServiceCheck,
  sendServiceStart,
  getServiceList,
  startSingleService,
  stopSingleService
} from '@/services/ant-design-pro/deploy';

// 配置检查
const configResult = await sendConfigCheck({
  sendData: { /* 配置数据 */ }
});

// 服务检查
const serviceResult = await sendServiceCheck({
  sendData: { /* 服务数据 */ }
});

// 启动服务
await sendServiceStart({
  sendData: { /* 启动参数 */ }
});

// 获取服务列表
const services = await getServiceList();

// 启动单个服务
await startSingleService({ service_id: 'service-123' });

// 停止单个服务
await stopSingleService({ service_id: 'service-123' });
```

### 3. Node API (`/api/node`)

```typescript
import { 
  checkDeployNodeIP,
  getDeployNodeList,
  registerDeployNode,
  updateDeployNode,
  removeDeployNode
} from '@/services/ant-design-pro/node';

// 检查节点IP
const ipCheckResult = await checkDeployNodeIP({ 
  ip: '*************' 
});

// 获取节点列表
const nodes = await getDeployNodeList();

// 注册新节点
await registerDeployNode({
  ip: '*************',
  name: '部署节点1',
  description: '主要部署节点'
});

// 更新节点信息
await updateDeployNode({
  nodeId: 'node-123',
  name: '更新后的名称',
  status: 'active'
});

// 删除节点
await removeDeployNode({ nodeId: 'node-123' });
```

## 错误处理

### 权限错误处理

系统会自动处理权限相关错误：

```typescript
// 401 - 认证失败
if (status === 401) {
  message.error('认证失败，请重新登录');
  keycloakService.login();
}

// 403 - 权限不足
if (status === 403) {
  const errorMsg = data?.message || '权限不足，需要管理员权限';
  message.error(errorMsg);
}
```

### 业务错误处理

```typescript
try {
  const result = await listProjects();
  // 处理成功结果
} catch (error) {
  // 错误会被全局错误处理器捕获并显示
  console.error('API调用失败:', error);
}
```

## 组件权限控制

### 使用Access组件

```typescript
import { Access, useAccess } from '@umijs/max';

const MyComponent = () => {
  const access = useAccess();
  
  return (
    <div>
      <Access accessible={access.isAdmin} fallback={<div>权限不足</div>}>
        <AdminPanel />
      </Access>
    </div>
  );
};
```

### 条件渲染

```typescript
const MyComponent = () => {
  const access = useAccess();
  
  return (
    <div>
      {access.isAdmin && (
        <Button onClick={handleAdminAction}>
          管理员操作
        </Button>
      )}
    </div>
  );
};
```

## 完整示例

参考 `web/src/components/AdminAPIExample.tsx` 文件，该文件展示了：

1. 权限检查
2. API调用
3. 错误处理
4. 用户界面交互

## 注意事项

1. **权限检查**：始终在调用管理员API前检查用户权限
2. **错误处理**：合理处理401和403错误
3. **用户体验**：为无权限用户提供友好的提示信息
4. **安全性**：前端权限控制仅用于用户体验，真正的安全控制在后端
5. **Token刷新**：Keycloak会自动处理token刷新，无需手动处理

## 调试技巧

1. 检查浏览器开发者工具的Network标签，确认请求头包含Authorization
2. 查看控制台错误信息，了解具体的权限问题
3. 使用Keycloak管理界面检查用户角色配置
4. 测试时可以使用不同权限的用户账号验证功能
