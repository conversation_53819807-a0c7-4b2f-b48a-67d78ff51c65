import React, { useState, useEffect } from 'react';
import { Button, Card, message, Space, Table, Modal, Form, Input, Select } from 'antd';
import { useAccess } from '@umijs/max';
import { 
  listProjects, 
  createProject, 
  deleteProject 
} from '@/services/ant-design-pro/gitlab';
import { 
  getServiceList, 
  startSingleService, 
  stopSingleService 
} from '@/services/ant-design-pro/deploy';
import { 
  checkDeployNodeIP, 
  getDeployNodeList 
} from '@/services/ant-design-pro/node';

const AdminAPIExample: React.FC = () => {
  const access = useAccess();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState([]);
  const [services, setServices] = useState([]);
  const [nodes, setNodes] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 检查是否有管理员权限
  const isAdmin = access.isAdmin;

  useEffect(() => {
    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 并行加载数据
      const [projectsRes, servicesRes, nodesRes] = await Promise.allSettled([
        listProjects(),
        getServiceList(),
        getDeployNodeList()
      ]);

      if (projectsRes.status === 'fulfilled') {
        setProjects(projectsRes.value || []);
      }
      if (servicesRes.status === 'fulfilled') {
        setServices(servicesRes.value?.data || []);
      }
      if (nodesRes.status === 'fulfilled') {
        setNodes(nodesRes.value?.data || []);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProject = async (values: any) => {
    try {
      await createProject(values);
      message.success('项目创建成功');
      setModalVisible(false);
      form.resetFields();
      loadData();
    } catch (error) {
      message.error('项目创建失败');
    }
  };

  const handleDeleteProject = async (projectId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个项目吗？',
      onOk: async () => {
        try {
          await deleteProject({ id: projectId });
          message.success('项目删除成功');
          loadData();
        } catch (error) {
          message.error('项目删除失败');
        }
      },
    });
  };

  const handleServiceAction = async (serviceId: string, action: 'start' | 'stop') => {
    try {
      if (action === 'start') {
        await startSingleService({ service_id: serviceId });
        message.success('服务启动成功');
      } else {
        await stopSingleService({ service_id: serviceId });
        message.success('服务停止成功');
      }
      loadData();
    } catch (error) {
      message.error(`服务${action === 'start' ? '启动' : '停止'}失败`);
    }
  };

  const handleCheckNodeIP = async () => {
    Modal.confirm({
      title: '检查节点IP',
      content: (
        <Form>
          <Form.Item name="ip" label="IP地址" rules={[{ required: true }]}>
            <Input placeholder="请输入IP地址" />
          </Form.Item>
        </Form>
      ),
      onOk: async (values) => {
        try {
          const result = await checkDeployNodeIP({ ip: values.ip });
          if (result.success) {
            message.success('节点IP检查成功');
          } else {
            message.error(result.message || '节点IP检查失败');
          }
        } catch (error) {
          message.error('节点IP检查失败');
        }
      },
    });
  };

  // 如果不是管理员，显示权限不足提示
  if (!isAdmin) {
    return (
      <Card title="管理员功能">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <h3>权限不足</h3>
          <p>此功能需要管理员权限才能访问</p>
        </div>
      </Card>
    );
  }

  const projectColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '项目名称', dataIndex: 'name', key: 'name' },
    { title: '描述', dataIndex: 'description', key: 'description' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          danger 
          size="small" 
          onClick={() => handleDeleteProject(record.id)}
        >
          删除
        </Button>
      ),
    },
  ];

  const serviceColumns = [
    { title: '服务ID', dataIndex: 'id', key: 'id' },
    { title: '服务名称', dataIndex: 'name', key: 'name' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="primary" 
            size="small" 
            onClick={() => handleServiceAction(record.id, 'start')}
          >
            启动
          </Button>
          <Button 
            danger 
            size="small" 
            onClick={() => handleServiceAction(record.id, 'stop')}
          >
            停止
          </Button>
        </Space>
      ),
    },
  ];

  const nodeColumns = [
    { title: '节点ID', dataIndex: 'id', key: 'id' },
    { title: 'IP地址', dataIndex: 'ip', key: 'ip' },
    { title: '名称', dataIndex: 'name', key: 'name' },
    { title: '状态', dataIndex: 'status', key: 'status' },
  ];

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* DevOps 项目管理 */}
        <Card 
          title="DevOps 项目管理" 
          extra={
            <Button type="primary" onClick={() => setModalVisible(true)}>
              创建项目
            </Button>
          }
        >
          <Table 
            columns={projectColumns} 
            dataSource={projects} 
            loading={loading}
            rowKey="id"
            size="small"
          />
        </Card>

        {/* 部署服务管理 */}
        <Card title="部署服务管理">
          <Table 
            columns={serviceColumns} 
            dataSource={services} 
            loading={loading}
            rowKey="id"
            size="small"
          />
        </Card>

        {/* 部署节点管理 */}
        <Card 
          title="部署节点管理"
          extra={
            <Button onClick={handleCheckNodeIP}>
              检查节点IP
            </Button>
          }
        >
          <Table 
            columns={nodeColumns} 
            dataSource={nodes} 
            loading={loading}
            rowKey="id"
            size="small"
          />
        </Card>
      </Space>

      {/* 创建项目模态框 */}
      <Modal
        title="创建项目"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleCreateProject} layout="vertical">
          <Form.Item name="name" label="项目名称" rules={[{ required: true }]}>
            <Input placeholder="请输入项目名称" />
          </Form.Item>
          <Form.Item name="description" label="项目描述">
            <Input.TextArea placeholder="请输入项目描述" />
          </Form.Item>
          <Form.Item name="visibility" label="可见性" rules={[{ required: true }]}>
            <Select placeholder="请选择可见性">
              <Select.Option value="private">私有</Select.Option>
              <Select.Option value="internal">内部</Select.Option>
              <Select.Option value="public">公开</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminAPIExample;
