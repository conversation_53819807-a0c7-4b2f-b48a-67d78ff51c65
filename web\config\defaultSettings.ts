import { ProLayoutProps } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} =
{
  "navTheme": "light",
  "layout": "side",
  "contentWidth": "Fluid",
  "fixedHeader": false,
  "fixSiderbar": true,
  "pwa": true,
  title:'芯合跨架构智算软件工厂',
  "siderWidth": 240,
  "splitMenus": false,

  "token": {
    "header": {
      "heightLayoutHeader": 56,
      "colorBgHeader": "#ffffff",
      "colorHeaderTitle": "#1f2937",
      "colorTextRightActionsItem": "#6b7280"
    },
    "sider": {
      "colorMenuBackground": "#f8fafc",
      "colorTextMenuTitle": "#1f2937",
      "colorTextMenu": "#6b7280",
      "colorTextMenuSelected": "#1f2937",
      "colorTextMenuActive": "#1f2937",
      "colorTextMenuItemHover": "#1f2937",
      "colorBgMenuItemHover": "#f1f5f9",
      "colorBgMenuItemSelected": "#e2e8f0",
      "colorBgCollapsedButton": "#ffffff",
      "colorMenuItemDivider": "#e2e8f0",
      "colorTextMenuSecondary": "#9ca3af",
      "colorBgMenuItemCollapsedElevated": "#ffffff",
      "colorTextCollapsedButton": "#6b7280",
      "colorTextCollapsedButtonHover": "#1f2937",
      "paddingInlineLayoutMenu": 16,
      "paddingBlockLayoutMenu": 8
    },
    "pageContainer": {
      "colorBgPageContainer": "#f8fafc",
      "paddingInlinePageContainerContent": 24,
      "paddingBlockPageContainerContent": 24
    }
  },
  "colorPrimary": "#3b82f6"
}


export default Settings;
