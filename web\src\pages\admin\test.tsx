import React, { useState } from 'react';
import { Card, Button, Space, message, Descriptions, Typography, Alert } from 'antd';
import { useAccess } from '@umijs/max';
import { listProjects } from '@/services/ant-design-pro/gitlab';
import { getServiceList } from '@/services/ant-design-pro/deploy';
import { checkDeployNodeIP } from '@/services/ant-design-pro/node';
import DebugUserInfo from '@/components/DebugUserInfo';

const { Title, Text } = Typography;

const AdminAPITest: React.FC = () => {
  const access = useAccess();
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<any>({});

  const isAdmin = access.isAdmin;

  const testDevOpsAPI = async () => {
    setLoading(true);
    try {
      const result = await listProjects();
      setTestResults(prev => ({
        ...prev,
        devops: { success: true, data: result, message: '获取项目列表成功' }
      }));
      message.success('DevOps API 测试成功');
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        devops: { success: false, error: error.message, message: 'DevOps API 测试失败' }
      }));
      message.error('DevOps API 测试失败');
    } finally {
      setLoading(false);
    }
  };

  const testDeployAPI = async () => {
    setLoading(true);
    try {
      const result = await getServiceList();
      setTestResults(prev => ({
        ...prev,
        deploy: { success: true, data: result, message: '获取服务列表成功' }
      }));
      message.success('Deploy API 测试成功');
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        deploy: { success: false, error: error.message, message: 'Deploy API 测试失败' }
      }));
      message.error('Deploy API 测试失败');
    } finally {
      setLoading(false);
    }
  };

  const testNodeAPI = async () => {
    setLoading(true);
    try {
      const result = await checkDeployNodeIP({ ip: '127.0.0.1' });
      setTestResults(prev => ({
        ...prev,
        node: { success: true, data: result, message: '节点IP检查成功' }
      }));
      message.success('Node API 测试成功');
    } catch (error: any) {
      setTestResults(prev => ({
        ...prev,
        node: { success: false, error: error.message, message: 'Node API 测试失败' }
      }));
      message.error('Node API 测试失败');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults({});
  };

  if (!isAdmin) {
    return (
      <Card title="管理员API测试">
        <Alert
          message="权限不足"
          description="此页面需要管理员权限才能访问。请确保您的账户具有admin角色。"
          type="warning"
          showIcon
        />
      </Card>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>管理员API测试页面</Title>

      <Alert
        message="测试说明"
        description="此页面用于测试管理员权限的API接口。所有请求都会自动添加Keycloak认证token。"
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* 调试信息 */}
      <DebugUserInfo />

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 测试按钮区域 */}
        <Card title="API测试" size="small">
          <Space wrap>
            <Button 
              type="primary" 
              onClick={testDevOpsAPI} 
              loading={loading}
            >
              测试 DevOps API
            </Button>
            <Button 
              type="primary" 
              onClick={testDeployAPI} 
              loading={loading}
            >
              测试 Deploy API
            </Button>
            <Button 
              type="primary" 
              onClick={testNodeAPI} 
              loading={loading}
            >
              测试 Node API
            </Button>
            <Button onClick={clearResults}>
              清除结果
            </Button>
          </Space>
        </Card>

        {/* 测试结果显示 */}
        {Object.keys(testResults).length > 0 && (
          <Card title="测试结果" size="small">
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {testResults.devops && (
                <Card size="small" title="DevOps API (/api/devops)" type="inner">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="状态">
                      <Text type={testResults.devops.success ? 'success' : 'danger'}>
                        {testResults.devops.success ? '成功' : '失败'}
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="消息">
                      {testResults.devops.message}
                    </Descriptions.Item>
                    {testResults.devops.error && (
                      <Descriptions.Item label="错误">
                        <Text type="danger">{testResults.devops.error}</Text>
                      </Descriptions.Item>
                    )}
                    {testResults.devops.data && (
                      <Descriptions.Item label="数据">
                        <Text code>{JSON.stringify(testResults.devops.data, null, 2)}</Text>
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Card>
              )}

              {testResults.deploy && (
                <Card size="small" title="Deploy API (/api/deploy)" type="inner">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="状态">
                      <Text type={testResults.deploy.success ? 'success' : 'danger'}>
                        {testResults.deploy.success ? '成功' : '失败'}
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="消息">
                      {testResults.deploy.message}
                    </Descriptions.Item>
                    {testResults.deploy.error && (
                      <Descriptions.Item label="错误">
                        <Text type="danger">{testResults.deploy.error}</Text>
                      </Descriptions.Item>
                    )}
                    {testResults.deploy.data && (
                      <Descriptions.Item label="数据">
                        <Text code>{JSON.stringify(testResults.deploy.data, null, 2)}</Text>
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Card>
              )}

              {testResults.node && (
                <Card size="small" title="Node API (/api/node)" type="inner">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="状态">
                      <Text type={testResults.node.success ? 'success' : 'danger'}>
                        {testResults.node.success ? '成功' : '失败'}
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="消息">
                      {testResults.node.message}
                    </Descriptions.Item>
                    {testResults.node.error && (
                      <Descriptions.Item label="错误">
                        <Text type="danger">{testResults.node.error}</Text>
                      </Descriptions.Item>
                    )}
                    {testResults.node.data && (
                      <Descriptions.Item label="数据">
                        <Text code>{JSON.stringify(testResults.node.data, null, 2)}</Text>
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Card>
              )}
            </Space>
          </Card>
        )}

        {/* 使用说明 */}
        <Card title="使用说明" size="small">
          <Space direction="vertical">
            <Text>1. 确保您的账户具有admin角色</Text>
            <Text>2. 点击测试按钮会调用相应的管理员API</Text>
            <Text>3. 请求会自动添加Keycloak认证token</Text>
            <Text>4. 如果返回403错误，说明权限不足</Text>
            <Text>5. 如果返回401错误，说明认证失败，需要重新登录</Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default AdminAPITest;
