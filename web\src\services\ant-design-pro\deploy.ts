import { request } from '@umijs/max';

export async function sendUDPMessage(body: API.UDPMessage, options?: { [key: string]: any }) {
  return request<{
    data: API.receiveUDPMessage[];
  }>('/api/udp/send', {
    method: 'POST',
    ...(options || {}),
  });
}

export async function sendConfigCheck(body: API.HTTPMessage, options?: { [key: string]: any }) {
  return request<{
    data: string;
  }>('/api/deploy/configcheck', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}
export async function sendServiceCheck(body: API.HTTPMessage, options?: { [key: string]: any }) {
  return request<{
    data: string;
  }>('/api/deploy/servicecheck', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function sendServiceStart(body:API.HTTPMessage, options?: { [key: string]: any }) {
  return request<{
    data: string;
  }>('/api/deploy/servicestart', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function sendToChat(baseURL: string, body: string, options?: { [key: string]: any }) {
  console.log('Base URL:', baseURL);
  return request<{
    data: string;
  }>('/chat', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function createClient(options?: { [key: string]: any }){
  return request<API.ListProjects[]>('/api/deploy/chat', {
    method: 'POST',
    ...(options || {}),
  });
}

// 启动聊天服务
export async function startChat(options?: { [key: string]: any }) {
  return request<{
    data: string;
  }>('/api/deploy/startchat', {
    method: 'POST',
    ...(options || {}),
  });
}

// 启动单个服务
export async function startSingleService(body: { service_id: string }, options?: { [key: string]: any }) {
  return request<{
    data: string;
  }>('/api/deploy/service/start', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 停止单个服务
export async function stopSingleService(body: { service_id: string }, options?: { [key: string]: any }) {
  return request<{
    data: string;
  }>('/api/deploy/service/stop', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 获取服务列表
export async function getServiceList(options?: { [key: string]: any }) {
  return request<{
    data: any[];
  }>('/api/deploy/service/list', {
    method: 'GET',
    ...(options || {}),
  });
}

// 获取单个服务信息
export async function getSingleService(body: { service_id: string }, options?: { [key: string]: any }) {
  return request<{
    data: any;
  }>('/api/deploy/service/get', {
    method: 'GET',
    params: body,
    ...(options || {}),
  });
}

// 删除服务
export async function removeService(body: { service_id: string }, options?: { [key: string]: any }) {
  return request<{
    data: string;
  }>('/api/deploy/service/remove', {
    method: 'DELETE',
    data: body,
    ...(options || {}),
  });
}