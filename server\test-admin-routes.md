# 管理员路由权限测试指南

## 测试目的
验证 `/api/devops`、`/api/deploy`、`/api/node` 路由是否正确实施了管理员权限控制。

## 测试前提条件
1. Keycloak 服务器正在运行
2. 后端服务器正在运行
3. 有两个测试用户：
   - 管理员用户（具有 `admin` 角色）
   - 普通用户（不具有 `admin` 角色）

## 测试步骤

### 1. 测试未认证访问
```bash
# 应该返回 401 Unauthorized
curl -X GET http://localhost:8001/api/devops/project/list
curl -X GET http://localhost:8001/api/deploy/service/list  
curl -X POST http://localhost:8001/api/node/checkIP
```

### 2. 测试普通用户访问
```bash
# 使用普通用户token，应该返回 403 Forbidden
curl -X GET http://localhost:8001/api/devops/project/list \
  -H "Authorization: Bearer <普通用户token>"

curl -X GET http://localhost:8001/api/deploy/service/list \
  -H "Authorization: Bearer <普通用户token>"

curl -X POST http://localhost:8001/api/node/checkIP \
  -H "Authorization: Bearer <普通用户token>"
```

### 3. 测试管理员用户访问
```bash
# 使用管理员token，应该返回正常响应
curl -X GET http://localhost:8001/api/devops/project/list \
  -H "Authorization: Bearer <管理员token>"

curl -X GET http://localhost:8001/api/deploy/service/list \
  -H "Authorization: Bearer <管理员token>"

curl -X POST http://localhost:8001/api/node/checkIP \
  -H "Authorization: Bearer <管理员token>" \
  -H "Content-Type: application/json" \
  -d '{"ip": "*************"}'
```

## 预期结果

### 未认证访问
- 状态码：401
- 响应：Keycloak 认证错误信息

### 普通用户访问
- 状态码：403
- 响应：
```json
{
  "success": false,
  "message": "需要管理员权限才能访问此资源",
  "requiredRole": "admin"
}
```

### 管理员用户访问
- 状态码：200 或相应的业务状态码
- 响应：正常的业务数据

## 权限控制流程

1. **Keycloak 认证**：`keycloak.protect()` 验证 JWT token
2. **管理员权限检查**：`requireAdmin` 中间件检查用户是否具有 `admin` 角色
3. **业务逻辑执行**：通过权限检查后执行具体的业务逻辑

## 注意事项

- 确保 Keycloak 中正确配置了 `admin` 角色
- 测试用户需要正确分配角色
- Token 需要是有效且未过期的
- 测试时注意替换实际的 token 值
