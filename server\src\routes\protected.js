const express = require('express');
const { requireRole } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// 注意：虽然前端Antd已实现权限控制，但后端仍需保留基础的身份验证
// 前端权限控制主要用于用户体验，后端验证确保API安全

// 基础受保护路由 - 仅需要身份验证，不需要特定角色
router.get('/dashboard', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: '欢迎访问仪表板',
    data: {
      user: req.kauth?.grant?.access_token?.content || req.user,
      timestamp: new Date().toISOString()
    }
  });
}));

// 管理员专用数据 - 保留角色验证以确保数据安全
router.get('/admin', requireRole('admin'), asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: '管理员专用区域',
    data: {
      adminData: '这是管理员才能看到的数据',
      timestamp: new Date().toISOString()
    }
  });
}));

// 用户数据 - 简化权限检查，主要依赖前端控制
router.get('/user-area', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: '用户区域',
    data: {
      userData: '这是用户可以访问的数据',
      userRoles: req.user?.roles || [],
      timestamp: new Date().toISOString()
    }
  });
}));

// 获取系统信息（需要认证但不需要特定角色）
router.get('/system-info', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      version: '1.0.0',
      environment: process.env.NODE_ENV,
      keycloakRealm: process.env.KEYCLOAK_REALM,
      timestamp: new Date().toISOString(),
      user: {
        id: req.kauth?.grant?.access_token?.content?.sub || req.user?.id,
        username: req.kauth?.grant?.access_token?.content?.preferred_username || req.user?.username
      }
    }
  });
}));

// 文件上传示例（需要认证）
router.post('/upload', asyncHandler(async (req, res) => {
  // 这里可以实现文件上传逻辑
  res.json({
    success: true,
    message: '文件上传功能待实现',
    data: {
      uploadedBy: req.kauth?.grant?.access_token?.content?.preferred_username || req.user?.username,
      timestamp: new Date().toISOString()
    }
  });
}));

// 数据查询示例
router.get('/data', asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, search = '' } = req.query;
  
  // 模拟数据查询
  const mockData = Array.from({ length: parseInt(limit) }, (_, index) => ({
    id: (parseInt(page) - 1) * parseInt(limit) + index + 1,
    name: `数据项 ${(parseInt(page) - 1) * parseInt(limit) + index + 1}`,
    description: `这是第 ${(parseInt(page) - 1) * parseInt(limit) + index + 1} 个数据项`,
    createdAt: new Date().toISOString(),
    createdBy: req.kauth?.grant?.access_token?.content?.preferred_username || req.user?.username
  }));

  res.json({
    success: true,
    data: {
      items: mockData,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: 100, // 模拟总数
        totalPages: Math.ceil(100 / parseInt(limit))
      },
      search
    }
  });
}));

module.exports = router;
