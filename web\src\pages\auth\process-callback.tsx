import React, { useEffect, useState } from 'react';
import { Spin, Result } from 'antd';
import { useModel, history } from '@umijs/max';
import keycloak from '../../../config/keycloak';

const ProcessCallback: React.FC = () => {
  const { setInitialState } = useModel('@@initialState');
  const [processing, setProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      try {
        console.log('=== 处理Keycloak回调开始 ===');
        console.log('当前URL:', window.location.href);
        
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const redirectPath = urlParams.get('redirect') || '/Console/projects';
        
        console.log('回调参数:', { 
          code: code ? '存在' : '不存在', 
          redirectPath 
        });

        if (!code) {
          throw new Error('缺少授权码');
        }

        // 使用授权码初始化Keycloak
        console.log('开始Keycloak初始化...');
        const authenticated = await keycloak.init({
          onLoad: 'login-required',
          checkLoginIframe: false,
        });

        console.log('Keycloak认证结果:', authenticated);

        if (authenticated && keycloak.tokenParsed) {
          // 获取用户信息
          const userInfo = {
            id: keycloak.tokenParsed.sub,
            username: keycloak.tokenParsed.preferred_username,
            email: keycloak.tokenParsed.email,
            name: keycloak.tokenParsed.name,
            roles: keycloak.tokenParsed.realm_access?.roles || [],
          };

          // 确定用户的访问级别
          const roles = userInfo.roles || [];
          let access = 'user'; // 默认权限

          if (roles.includes('owner')) {
            access = 'owner';
          } else if (roles.includes('admin')) {
            access = 'admin';
          } else if (roles.includes('maintainer')) {
            access = 'maintainer';
          }

          console.log('用户信息:', userInfo, '访问级别:', access);

          // 更新全局状态
          await setInitialState((prevState: any) => ({
            ...prevState,
            currentUser: {
              userid: userInfo.id,
              name: userInfo.name,
              username: userInfo.username,
              email: userInfo.email,
              avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo.name || userInfo.username)}&background=1890ff&color=fff`,
              roles: roles,
              access: access,
            },
          }));

          console.log('认证成功，即将重定向到:', redirectPath);
          
          // 使用 history.replace 替代 window.location.replace
          history.replace(redirectPath);
        } else {
          throw new Error('认证失败');
        }
      } catch (error) {
        console.error('处理回调失败:', error);
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        setError(`认证失败: ${errorMessage}`);
        setProcessing(false);
        
        // 3秒后重定向到登录页面
        setTimeout(() => {
          window.location.replace('/user/login');
        }, 3000);
      }
    };

    processCallback();
  }, [setInitialState]);

  if (error) {
    return (
      <div style={{ 
        height: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Result
          status="error"
          title="认证失败"
          subTitle={error}
          extra={
            <div style={{ color: '#fff', marginTop: 16 }}>
              3秒后将自动跳转到登录页面...
            </div>
          }
          style={{ 
            background: 'rgba(255, 255, 255, 0.9)', 
            borderRadius: 8, 
            padding: 24,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
          }}
        />
      </div>
    );
  }

  return (
    <div style={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: '#fff'
    }}>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderRadius: 16,
        padding: 48,
        textAlign: 'center',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <Spin size="large" style={{ marginBottom: 24 }} />
        <h2 style={{ 
          color: '#fff', 
          marginBottom: 16, 
          fontSize: 24,
          fontWeight: 300
        }}>
          登录成功！
        </h2>
        <p style={{ 
          color: 'rgba(255, 255, 255, 0.8)', 
          fontSize: 16,
          margin: 0
        }}>
          正在为您跳转到应用...
        </p>
      </div>
    </div>
  );
};

export default ProcessCallback;
